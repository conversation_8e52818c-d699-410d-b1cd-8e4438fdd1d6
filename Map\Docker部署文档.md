# 湖北省地图API服务 Docker部署命令

## 快速部署流程

### 1. 生成Dockerfile文件
如果项目中没有Dockerfile，可以用以下命令快速创建：
```bash
# 快速生成Dockerfile
cat > Dockerfile << 'EOF'
# 使用官方Go镜像作为构建环境
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 使用阿里云镜像源加速包下载
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 设置Go模块代理为阿里云
ENV GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
ENV GOSUMDB=sum.golang.google.cn

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 使用轻量级镜像作为运行环境
FROM alpine:latest

# 使用阿里云镜像源加速包下载
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装ca-certificates用于HTTPS请求
RUN apk --no-cache add ca-certificates tzdata

# 设置工作目录
WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 设置时区
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8081

# 运行应用
CMD ["./main"]
EOF
```

或者手动创建Dockerfile文件，内容如上所示。

### 2. 构建镜像
```bash
# 进入项目目录
cd Map

# 构建Docker镜像
docker build -t hubei-map-api:latest .
```

### 3. 运行容器
```bash
# 运行容器
docker run -d \
  --name hubei-map-api \
  -p 8081:8081 \
  -e TZ=Asia/Shanghai \
  --restart unless-stopped \
  hubei-map-api:latest
```

### 4. 验证部署
```bash
# 查看容器状态
docker ps

# 查看日志
docker logs hubei-map-api

# 测试API
curl http://localhost:8081/api/v1/hubei
```

### 5. 容器管理
```bash
# 停止容器
docker stop hubei-map-api

# 启动容器
docker start hubei-map-api

# 重启容器
docker restart hubei-map-api

# 删除容器
docker rm hubei-map-api

# 删除镜像
docker rmi hubei-map-api:latest
```

## Docker Compose部署（可选）

创建 `docker-compose.yml` 文件：
```yaml
version: '3.8'
services:
  hubei-map-api:
    build: .
    container_name: hubei-map-api
    ports:
      - "8081:8081"
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
```

使用Docker Compose部署：
```bash
# 构建并启动
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 常用故障排除

```bash
# 端口被占用时使用其他端口
docker run -d --name hubei-map-api -p 8082:8081 hubei-map-api:latest

# 查看详细错误信息
docker logs hubei-map-api

# 进入容器调试
docker exec -it hubei-map-api sh
```
