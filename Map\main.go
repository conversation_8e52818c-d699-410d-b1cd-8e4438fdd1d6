package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// GeoData 地理数据结构
type GeoData struct {
	Type     string    `json:"type"`
	Features []Feature `json:"features"`
}

// Feature 地理特征结构
type Feature struct {
	Type       string                 `json:"type"`
	Properties map[string]interface{} `json:"properties"`
	Geometry   Geometry               `json:"geometry"`
}

// Geometry 几何结构
type Geometry struct {
	Type        string          `json:"type"`
	Coordinates json.RawMessage `json:"coordinates"`
}

// APIResponse 统一API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 获取地理数据
func fetchGeoData(url string) (*GeoData, error) {
	client := &http.Client{Timeout: 30 * time.Second}

	resp, err := client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON数据
	var geoData GeoData
	if err := json.Unmarshal(body, &geoData); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	return &geoData, nil
}

// 获取湖北省数据
func getHubeiData(c *gin.Context) {
	geoData, err := fetchGeoData("https://geo.datav.aliyun.com/areas_v3/bound/420000_full.json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("获取数据失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "获取湖北省地理数据成功",
		Data:    geoData,
	})
}

// 获取指定区域代码的数据
func getAreaData(c *gin.Context) {
	areaCode := c.Param("code")
	if areaCode == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "区域代码不能为空",
			Data:    nil,
		})
		return
	}

	// 验证区域代码格式（6位数字）
	if len(areaCode) != 6 {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "区域代码格式错误，应为6位数字",
		})
		return
	}

	if _, err := strconv.Atoi(areaCode); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "区域代码必须为数字",
		})
		return
	}

	dataSource := fmt.Sprintf("https://geo.datav.aliyun.com/areas_v3/bound/%s_full.json", areaCode)
	geoData, err := fetchGeoData(dataSource)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("获取区域数据失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: fmt.Sprintf("获取区域 %s 地理数据成功", areaCode),
		Data:    geoData,
	})
}

func main() {
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Recovery())

	// 配置CORS - 只允许GET和OPTIONS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type"}
	r.Use(cors.New(config))

	// API路由组
	api := r.Group("/api/v1")
	{
		// 获取湖北省数据
		api.GET("/hubei", getHubeiData)

		// 获取指定区域数据
		api.GET("/area/:code", getAreaData)
	}

	// 启动服务器
	port := ":8081"

	if err := r.Run(port); err != nil {
		panic(fmt.Sprintf("服务启动失败: %v", err))
	}
}
