<template>
  <div class="evaluation-standard">
    <h1 class="title">湖北省高标准农田建设评价标准</h1>
    
    <table class="standard-table">
      <thead>
        <tr>
          <th class="header-col1">评价指标分值</th>
          <th class="header-col2">评价标准</th>
          <th class="header-col3">评价标据</th>
          <th class="header-col4">佐证材料</th>
        </tr>
      </thead>
      <tbody>
        <!-- 前期工作部分 -->
        <tr>
          <td rowspan="2" class="category-cell">
            <div class="category-title">前期工作</div>
            <div class="category-score">(8分)</div>
          </td>
          <td class="sub-item">
            <div class="item-name">项目论证</div>
            <div class="item-score">6分</div>
          </td>
          <td class="standard-content">
            自然资源主管部门按照标准农田建设任务及时落实到具体地块，按时完成项目初步设计并通过审查；
            项目初步设计文件齐全，设计深度符合要求，第4分；未按时完成项目初步设计或审查的，得2分；
            按时间和质量均分，在时限要求内编报农田建设综合监测监管系统的，得2分，未按时或未按
            要求填报的，在此基础上扣减重点分。
          </td>
          <td class="evidence-content">
            县级自评提供及资自部门文件、年度项目实施计划备案材料；
            全国农田建设综合监测监管平台数据。
          </td>
          <td class="material-content">1（材料全部）</td>
        </tr>
        
        <tr>
          <td class="sub-item">
            <div class="item-name">项目储备</div>
            <div class="item-score">2分</div>
          </td>
          <td class="standard-content">
            根据规划编制要求，建立项目储备库的，得2分；否则不得分。
          </td>
          <td class="evidence-content">
            县级自评提供及资自部门备库证明材料。
          </td>
          <td class="material-content"></td>
        </tr>

        <!-- 建设与质量部分 -->
        <tr>
          <td rowspan="5" class="category-cell">
            <div class="category-title">建设与质量</div>
            <div class="category-score">(35分)</div>
          </td>
          <td class="sub-item">
            <div class="item-name">任务完成情况</div>
            <div class="item-score">10分</div>
          </td>
          <td class="standard-content">
            以当年12月31日为节点，任务高标准农田（含高效节水灌溉，下同）建设任务完成的，得10分；
            未完成的，每减少10%（不足10%，按10%算），扣2分，扣完为止。
          </td>
          <td class="evidence-content">
            全国农田建设综合监测监管平台数据。
            （任务完成任务的自查核实数据与全国平台数据）
          </td>
          <td class="material-content"></td>
        </tr>

        <tr>
          <td class="sub-item">
            <div class="item-name">当年项目建设进度</div>
            <div class="item-score">15分</div>
          </td>
          <td class="standard-content">
            以当年12月31日为节点，当年立项项目开工完成100%，得10分；未完成的，每减少5%（不足5%，
            按5%算），扣1分，扣完为止。
            <br><br>
            以当年12月31日为节点，当年立项目建设进度完成标准信息农田面积达到审核50%的，得5分；未
            达到的，每减少10%（不足10%，按10%算），扣1分，扣完为止。
          </td>
          <td class="evidence-content">
            全国农田建设综合监测监管平台数据。
          </td>
          <td class="material-content"></td>
        </tr>

        <tr>
          <td class="sub-item">
            <div class="item-name">工程质量</div>
            <div class="item-score">5分</div>
          </td>
          <td class="standard-content">
            工程设施质量达到设计要求的，得5分。土地平整工程、土壤改良工程、灌溉与排水工程、田间
            道路工程、农田防护与生态环境保护工程、农田输配电工程等工程，每发现一项质量未达到设计
            要求的，扣1分，扣完为止。
          </td>
          <td class="evidence-content">
            省农业农村厅开展监测情况和实地评价数据。
          </td>
          <td class="material-content"></td>
        </tr>

        <tr>
          <td class="sub-item">
            <div class="item-name">耕地质量建设</div>
            <div class="item-score">5分</div>
          </td>
          <td class="standard-content">
            在高标准农田建设区实施耕地质量提升与保护技术措施（如增施有机肥料、培肥还田等），提
            高耕地基础地力或保持与提升耕地基础地力80%以上的3分（不足80%，按1%算），每下降1%扣0.1分，
            扣0.5分，扣完为止。在高标准农田建设区实施耕地质量提升保护工作，得2分，未开
            展的，不得分。
          </td>
          <td class="evidence-content">
            县级自评提供以及综合耕地质量建设项目自查利用高标准农田建设区项目自查，
            或利用明确备案登记备案农田建设区田土壤检测地质量保护与提升技术措施的相
            关证明材料，按照《耕地质量等级》
            （GB/T33469-2016）评价。
          </td>
          <td class="material-content"></td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
// Vue 3 Composition API
import { ref } from 'vue'

// 可以在这里添加任何需要的响应式数据或方法
</script>

<style scoped>
.evaluation-standard {
  padding: 20px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: #f5f5f5;
}

.title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.standard-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.standard-table th,
.standard-table td {
  border: 1px solid #333;
  padding: 8px;
  text-align: left;
  vertical-align: top;
  font-size: 12px;
  line-height: 1.4;
}

.standard-table th {
  background-color: #f8f8f8;
  font-weight: bold;
  text-align: center;
}

.header-col1 { width: 12%; }
.header-col2 { width: 45%; }
.header-col3 { width: 28%; }
.header-col4 { width: 15%; }

.category-cell {
  background-color: #f0f0f0;
  text-align: center;
  font-weight: bold;
  width: 12%;
}

.category-title {
  font-size: 14px;
  margin-bottom: 5px;
}

.category-score {
  font-size: 12px;
  color: #666;
}

.sub-item {
  text-align: center;
  background-color: #f8f8f8;
  font-weight: bold;
}

.item-name {
  margin-bottom: 3px;
}

.item-score {
  font-size: 11px;
  color: #666;
}

.standard-content {
  padding: 10px;
  line-height: 1.5;
}

.evidence-content {
  padding: 8px;
  background-color: #fafafa;
  font-size: 11px;
}

.material-content {
  text-align: center;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .standard-table {
    font-size: 10px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .standard-table th,
  .standard-table td {
    padding: 6px;
  }
}
</style>
